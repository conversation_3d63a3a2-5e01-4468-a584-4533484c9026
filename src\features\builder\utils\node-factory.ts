import { TreeNode, TreeNodeType } from "../context/types";
import { NODE_DEFAULTS } from "../config/constants";

/**
 * Node creation and default value utilities
 */

// Generate unique node ID with type prefix
export const generateNodeId = (type: TreeNodeType = "frame"): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${type}-${timestamp}-${random}`;
};

// Create default node properties based on type
export const createDefaultNode = (type: TreeNodeType): Partial<TreeNode> => {
  const defaults: Record<TreeNodeType, Partial<TreeNode>> = {
    frame: {
      properties: {},
    },
    text: {
      properties: {
        content: NODE_DEFAULTS.TEXT_PLACEHOLDER,
      },
    },
    image: {
      properties: {
        src: "",
        alt: "",
      },
    },
    video: {
      properties: {
        src: "",
      },
    },
    imageWithCaption: {
      properties: {
        src: "",
        alt: "",
        caption: "Caption text",
      },
    },
    dateTimeCard: {
      properties: {
        showDate: true,
        showTime: true,
      },
    },
    yourNewNodeType: {
      properties: {
        someProperty: "Default value",
        anotherProperty: true,
      },
      group: "widget", // Specify the group here
    },
  };

  return defaults[type] || {};
};

// Create a new node with defaults (for testing/samples)
export const createNode = (type: TreeNodeType, label?: string): TreeNode => {
  const defaults = createDefaultNode(type);
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);

  return {
    id: `${type}-${timestamp}-${random}`,
    label: label || `Sample ${type}`,
    type,
    group: "basic",
    children: [],
    ...defaults,
  };
};

// Create root node
export const createRootNode = (): TreeNode => ({
  id: "root",
  label: "Nội dung",
  type: "frame",
  group: "basic",
  children: [],
});

// Create sample content for testing
export const createSampleContent = (): TreeNode => {
  const root = createRootNode();

  const header = createNode("frame", "Header");
  header.style = { className: "p-6 bg-gray-100" };

  const title = createNode("text", "Title");
  title.properties = {
    content: "Welcome to Builder",
  };
  title.style = { className: "text-2xl font-bold" };

  header.children.push(title);
  root.children.push(header);

  // Add image with content styles for testing
  const imageSection = createNode("frame", "Image Section");
  imageSection.style = { className: "p-4" };

  const testImage = createNode("image", "Test Image");
  testImage.properties = {
    src: "https://images.unsplash.com/photo-1682687220742-aba13b6e50ba",
    alt: "Test landscape image",
  };
  testImage.style = {
    className: "w-96 h-64 rounded-lg shadow-lg",
    variants: {
      image: "object-cover hover:scale-110 transition-transform duration-300",
    },
  };

  imageSection.children.push(testImage);
  root.children.push(imageSection);

  return root;
};
